/* Auth Component Styles */
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

.auth-card {
  background: rgba(45, 45, 45, 0.9);
  border-radius: 20px;
  padding: 60px 50px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-title {
  color: #ffffff;
  font-size: 2.5rem;
  font-weight: 300;
  text-align: center;
  margin-bottom: 40px;
  font-family: 'Brush Script MT', cursive;
  letter-spacing: 1px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-group {
  position: relative;
}

.auth-input {
  width: 100%;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.auth-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.auth-input:focus {
  outline: none;
  border-color: #4CAF50;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.auth-button {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  border: none;
  border-radius: 50px;
  color: white;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.auth-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.message {
  padding: 12px 20px;
  border-radius: 25px;
  text-align: center;
  margin-top: 15px;
  font-size: 14px;
  font-weight: 500;
}

.message.success {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.message.error {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.auth-switch {
  text-align: center;
  margin-top: 30px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.auth-link {
  color: #4CAF50;
  cursor: pointer;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #45a049;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-card {
    margin: 20px;
    padding: 40px 30px;
  }
  
  .auth-title {
    font-size: 2rem;
  }
  
  .auth-input, .auth-button {
    padding: 14px 18px;
    font-size: 16px;
  }
}
