.evaluation-report {
  min-height: 100vh;
  background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 20px;
}

.report-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: rgba(45, 45, 45, 0.8);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.report-header h1 {
  font-size: 2.5rem;
  font-weight: 300;
  margin: 0 0 20px 0;
  color: #ffffff;
}

.overall-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.score-percentage {
  font-size: 3rem;
  font-weight: bold;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4px solid;
}

.score-percentage.passed {
  color: #4CAF50;
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.score-percentage.failed {
  color: #f44336;
  border-color: #f44336;
  background: rgba(244, 67, 54, 0.1);
}

.score-details {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
}

.report-content {
  max-width: 1000px;
  margin: 0 auto;
}

/* Summary Section */
.summary-section {
  margin-bottom: 40px;
}

.summary-section h2 {
  font-size: 1.8rem;
  font-weight: 400;
  margin-bottom: 20px;
  color: #ffffff;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  background: rgba(45, 45, 45, 0.6);
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.summary-card.correct {
  border-color: rgba(76, 175, 80, 0.3);
  background: rgba(76, 175, 80, 0.1);
}

.summary-card.wrong {
  border-color: rgba(244, 67, 54, 0.3);
  background: rgba(244, 67, 54, 0.1);
}

.summary-card.accuracy {
  border-color: rgba(33, 150, 243, 0.3);
  background: rgba(33, 150, 243, 0.1);
}

.summary-card.result.passed {
  border-color: rgba(76, 175, 80, 0.5);
  background: rgba(76, 175, 80, 0.2);
}

.summary-card.result.failed {
  border-color: rgba(244, 67, 54, 0.5);
  background: rgba(244, 67, 54, 0.2);
}

.summary-number {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #ffffff;
}

.summary-label {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Wrong Answers Section */
.wrong-answers-section {
  margin-bottom: 40px;
  background: rgba(45, 45, 45, 0.4);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.wrong-answers-section h2 {
  font-size: 1.8rem;
  font-weight: 400;
  margin-bottom: 20px;
  color: #ffffff;
}

.review-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.review-counter {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.review-controls {
  display: flex;
  gap: 10px;
}

.nav-button {
  padding: 8px 16px;
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.nav-button:hover:not(:disabled) {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.question-review {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 25px;
}

.question-text h3 {
  color: #ffffff;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.question-text p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 25px;
}

.answers-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 25px;
}

.answer-section h4 {
  margin-bottom: 10px;
  font-size: 1rem;
}

.answer-option {
  padding: 15px;
  border-radius: 10px;
  font-weight: 500;
  border: 2px solid;
}

.answer-option.wrong {
  background: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.3);
  color: #f44336;
}

.answer-option.correct {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.explanation-section {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
}

.explanation-section h4 {
  color: #2196F3;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.explanation-content {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 15px;
}

.why-wrong-explanation {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.why-wrong-explanation h5 {
  color: #FFC107;
  margin-bottom: 10px;
  font-size: 1rem;
}

.topic-category {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
}

.topic-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.topic-name {
  background: rgba(156, 39, 176, 0.2);
  color: #9C27B0;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

/* Struggles Section */
.struggles-section {
  margin-bottom: 40px;
  background: rgba(45, 45, 45, 0.4);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.struggles-section h2 {
  font-size: 1.8rem;
  font-weight: 400;
  margin-bottom: 20px;
  color: #ffffff;
}

.struggles-list {
  margin-bottom: 25px;
}

.struggle-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  margin-bottom: 10px;
}

.struggle-rank {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

.struggle-info {
  flex: 1;
}

.struggle-topic {
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 5px;
}

.struggle-stats {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.struggle-bar {
  width: 100px;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.struggle-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFC107, #FF9800);
  transition: width 0.3s ease;
}

.overall-insights {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 20px;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.insight-item {
  text-align: center;
}

.insight-label {
  display: block;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 5px;
}

.insight-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #4CAF50;
}

/* Action Buttons */
.report-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
}

.action-button {
  padding: 15px 30px;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.action-button.retry {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.action-button.retry:hover {
  background: linear-gradient(135deg, #F57C00 0%, #FF9800 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

.action-button.continue {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.action-button.continue:hover {
  background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.action-button.genie {
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.action-button.genie:hover {
  background: linear-gradient(135deg, #F7931E 0%, #FF6B35 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .evaluation-report {
    padding: 15px;
  }
  
  .answers-comparison {
    grid-template-columns: 1fr;
  }
  
  .summary-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .report-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-button {
    width: 100%;
    max-width: 300px;
    min-width: auto;
    padding: 12px 20px;
    font-size: 14px;
  }
}
