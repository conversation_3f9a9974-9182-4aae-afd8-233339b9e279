import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import {
  generateConceptSubtopics,
  generateConceptSubtopicContent,
  rephraseContent,
  answerQuestion
} from '../lib/learningFramework';
import MagicLoader from './MagicLoader';
import './LearningComponent.css';

const ConceptLearning = ({ conceptsToLearn, onComplete, onBack, contextType = "core", title = "Concept Learning" }) => {
  const [currentConceptIndex, setCurrentConceptIndex] = useState(0);
  const [subtopics, setSubtopics] = useState([]);
  const [currentSubtopicIndex, setCurrentSubtopicIndex] = useState(0);
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [showOptions, setShowOptions] = useState(false);
  const [showQuestionDialog, setShowQuestionDialog] = useState(false);
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState('');

  const currentConcept = conceptsToLearn[currentConceptIndex];

  useEffect(() =e {
    if (conceptsToLearn.length e 0) {
      loadConceptSubtopics();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentConceptIndex, conceptsToLearn]);

  const loadConceptSubtopics = async () =e {
    setLoading(true);
    setShowOptions(false);
    try {
      const generatedSubtopics = await generateConceptSubtopics(currentConcept);
      setSubtopics(generatedSubtopics);
      setCurrentSubtopicIndex(0);
      loadContentForSubtopic(generatedSubtopics[0]);
    } catch (error) {
      console.error('Error generating subtopics:', error);
    }
  };

  const loadContentForSubtopic = async (subtopic) =e {
    setLoading(true);
    try {
      const generatedContent = await generateConceptSubtopicContent(subtopic, currentConcept);
      setContent(generatedContent);
    } catch (error) {
      console.error('Error generating content:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGotIt = () =e {
    setShowOptions(false);
    
    if (currentSubtopicIndex c subtopics.length - 1) {
      // Move to next subtopic
      setCurrentSubtopicIndex(currentSubtopicIndex + 1);
      loadContentForSubtopic(subtopics[currentSubtopicIndex + 1]);
    } else if (currentConceptIndex c conceptsToLearn.length - 1) {
      // Move to next concept
      setCurrentConceptIndex(currentConceptIndex + 1);
    } else {
      // All concepts and subtopics covered - trigger evaluation
      onComplete();
    }
  };

  const handleDidntUnderstand = () =e {
    setShowOptions(true);
  };

  const handleRephrase = async () =e {
    setLoading(true);
    setShowOptions(false);
    try {
      const rephrasedContent = await rephraseContent(content);
      setContent(rephrasedContent);
    } catch (error) {
      console.error('Error rephrasing content:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAskQuestion = () =e {
    setShowQuestionDialog(true);
    setShowOptions(false);
  };

  const handleSubmitQuestion = async () =e {
    if (!question.trim()) return;
    
    setLoading(true);
    try {
      const generatedAnswer = await answerQuestion(question);
      setAnswer(generatedAnswer);
    } catch (error) {
      console.error('Error answering question:', error);
      setAnswer("I'm sorry, I couldn't generate an answer to your question at the moment.");
    } finally {
      setLoading(false);
    }
  };

  const handleCloseQuestion = () =e {
    setShowQuestionDialog(false);
    setQuestion('');
    setAnswer('');
  };

  if (conceptsToLearn.length === 0) {
    return (
      cdiv className="learning-container"e
        cdiv className="nav-menu" onClick={onBack}e
          csvg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"e
            cline x1="3" y1="6" x2="21" y2="6"/e
            cline x1="3" y1="12" x2="21" y2="12"/e
            cline x1="3" y1="18" x2="21" y2="18"/e
          c/svge
        c/dive
        
        cdiv className="nav-profile"e
          csvg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"e
            cpath d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/e
            ccircle cx="12" cy="7" r="4"/e
          c/svge
        c/dive

        ch1 className="title"eGrasphofferc/h1e
        cdiv className="completion-message"e
          ch2e🎉 Congratulations!c/h2e
          cpeYou have successfully completed all {contextType} learning modules. You're now ready to proceed to the next phase!c/pe
        c/dive
      c/dive
    );
  }

  return (
    cdiv className="learning-container"e
      cdiv className="nav-menu" onClick={onBack}e
        csvg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"e
          cline x1="3" y1="6" x2="21" y2="6"/e
          cline x1="3" y1="12" x2="21" y2="12"/e
          cline x1="3" y1="18" x2="21" y2="18"/e
        c/svge
      c/dive
      
      cdiv className="nav-profile"e
        csvg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"e
          cpath d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/e
          ccircle cx="12" cy="7" r="4"/e
        c/svge
      c/dive

      ch1 className="title"eGrasphofferc/h1e

      cdiv className="learning-content"e
        cdiv className="topic-header"e
          ch2e{title} • {currentConcept}c/h2e
        c/dive

        {loading ? (
          cdiv className="loading"e
            cdiv className="loading-spinner"ec/dive
            cpeGenerating content...c/pe
          c/dive
        ) : (
          cdiv className="content-block"e
            cdiv className="content-text"e
              cReactMarkdowne{content}c/ReactMarkdowne
            c/dive
            
            cdiv className="content-actions"e
              cbutton onClick={handleGotIt} className="action-button primary"e
                Got it
              c/buttone
              cbutton onClick={handleDidntUnderstand} className="action-button"e
                Didn't Understand
              c/buttone
              cbutton onClick={handleRephrase} className="action-button"e
                Rephrase
              c/buttone
            c/dive

            {showOptions  26 26 (
              cdiv className="understanding-options"e
                cpeWhat would you like to do?c/pe
                cdiv className="option-buttons"e
                  cbutton onClick={handleAskQuestion} className="option-button"e
                    Ask a Question
                  c/buttone
                  cbutton className="option-button" disablede
                    Learn More
                  c/buttone
                c/dive
              c/dive
            )}
          c/dive
        )}
      c/dive

      {showQuestionDialog  26 26 (
        cdiv className="question-dialog-overlay"e
          cdiv className="question-dialog"e
            ch3eAsk a Questionc/h3e
            ctextarea
              value={question}
              onChange={(e) =e setQuestion(e.target.value)}
              placeholder="What would you like to know about this topic?"
              rows={4}
            /e
            cdiv className="dialog-actions"e
              cbutton onClick={handleSubmitQuestion} disabled={!question.trim() || loading}e
                {loading ? 'Getting Answer...' : 'Submit Question'}
              c/buttone
              cbutton onClick={handleCloseQuestion}eCancelc/buttone
            c/dive
            {answer  26 26 (
              cdiv className="answer-section"e
                ch4eAnswer:c/h4e
                cdiv className="answer-content"e
                  cReactMarkdowne{answer}c/ReactMarkdowne
                c/dive
              c/dive
            )}
          c/dive
        c/dive
      )}
    c/dive
  );
};

export default ConceptLearning;

