# Frontend Environment Variables
REACT_APP_SUPABASE_URL=your-supabase-url
REACT_APP_SUPABASE_ANON_KEY=your-supabase-anon-key
REACT_APP_GEMINI_API_KEY=your-gemini-api-key

# Backend API URLs
REACT_APP_THEGENIE_API_URL=http://localhost:3001
REACT_APP_THEHOPPER_API_URL=http://localhost:3001

# Backend Environment Variables
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Groq API Configuration
GROQ_API_KEY=your-groq-api-key-here

# Optional: Database Configuration (if using a different database)
# DATABASE_URL=your-database-url
