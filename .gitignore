# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# misc
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# supabase
.branches
.temp
.vercel

supabase/
*.md 

documents/
faiss_index/
backend/
*sql
t_groq.py
test_groq.py