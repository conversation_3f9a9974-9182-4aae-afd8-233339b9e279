{"name": "grasphopper", "version": "0.1.0", "private": true, "dependencies": {"@google/generative-ai": "^0.24.1", "@supabase/storage-js": "^2.11.0", "@supabase/supabase-js": "^2.56.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "dotenv": "^17.2.1", "pdfjs-dist": "^5.4.54", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "react-scripts": "^5.0.1", "tesseract.js": "^6.0.1", "tesseract.js-core": "^6.0.0", "uuid": "^11.1.0", "web-vitals": "^5.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --transformIgnorePatterns \"node_modules/(?!react-markdown|@google/generative-ai)/\"", "eject": "react-scripts eject", "server": "node backend/server.js", "dev": "concurrently \"npm run server\" \"npm start\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"transformIgnorePatterns": ["node_modules/(?!(@google/generative-ai|react-markdown)/)"], "testEnvironment": "jsdom", "setupFilesAfterEnv": ["@testing-library/jest-dom"]}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.31", "webpack-dev-server": "^4.15.1"}, "devDependencies": {"concurrently": "^9.2.1"}}